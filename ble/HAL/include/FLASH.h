#ifndef __FLASH_H_
#define __FLASH_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "CONFIG.h"

/**************************************************************************************************
 *                                              MACROS
 **************************************************************************************************/
#define CHECK_VALUE 0x5A // Detection value of whether the information saved in flash is valid
#define DEFAULT_PASSCODE 123456 // Default passcode, change this to your own

// Structure to store bonded device information
typedef struct {
    uint8_t CheckFlag;                                   // Valid flag (0x5A if valid)
    uint16_t InfoNum;                                    // Number of bonded devices stored
    uint8_t BondedDevAddr[PERIPHERAL_MAX_CONNECTION][6]; // MAC addresses of bonded devices
    uint8_t Reserve[5];                                  // Reserved for future use
    uint8_t Checksum;                                    // Checksum for data integrity
} BONDED_DEVICE_INFO_t;

typedef struct {
    uint8_t CheckFlag;
    uint32_t Passcode; // Passcode value
    uint8_t Checksum;  // Checksum for data integrity
} PASSCODE_INFO_t;

extern BONDED_DEVICE_INFO_t BondedDevInfo; // Bonded device information

extern PASSCODE_INFO_t PasscodeInfo; // Passcode information

uint8_t HAL_FlashChecksumCalculate(uint8_t *p_buf, uint16_t len);

/*
 * Check buffer checksum
 */
uint8_t HAL_FlashChecksumCheck(uint8_t *p_buf, uint16_t len);

/*
 * Save bonded device information to flash
 */
uint8_t HAL_SaveBondedDevInfo(void);

/*
 * Read bonded device information from flash
 */
uint8_t HAL_ReadBondedDevInfo(void);

/*
 * Add a device to the bonded device list
 */
uint8_t HAL_AddBondedDevice(uint8_t *addr);

/*
 * Remove a device from the bonded device list
 */
uint8_t HAL_RemoveBondedDevice(uint8_t *addr);

/*
 * Check if a device is in the bonded device list
 */
uint8_t HAL_IsBondedDevice(uint8_t *addr);

/*
 * Clear all bonded device information
 */
uint8_t HAL_ClearAllBondedDevices(void);

/*
 * Save passcode to flash
 */
uint8_t HAL_SavePasscode(uint32_t passcode);

/*
 * Read passcode from flash
 */
uint8_t HAL_ReadPasscode(void);

/*
 * Get the current passcode
 */
uint32_t HAL_GetPasscode(void);

/**************************************************************************************************
**************************************************************************************************/

#ifdef __cplusplus
}
#endif

#endif /* __FLASH_H_ */
