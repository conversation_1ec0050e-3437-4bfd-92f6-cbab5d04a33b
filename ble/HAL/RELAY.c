#include "RELAY.h"
#include "CH59x_common.h"

// Define relay control pin
#define RELAY_PIN  GPIO_Pin_4 // PA4
#define RELAY_PORT GPIOA

void Relay_Init(void)
{
    // Configure PA4 as push-pull output
    GPIOA_ModeCfg(RELAY_PIN, GPIO_ModeOut_PP_5mA);
}

void Relay_On(void)
{
    GPIOA_ResetBits(RELAY_PIN); // Active low relay
}

void Relay_Off(void)
{
    GPIOA_SetBits(RELAY_PIN); // Deactivate relay
}