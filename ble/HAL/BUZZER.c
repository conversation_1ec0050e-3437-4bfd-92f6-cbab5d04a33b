#include "CH59x_common.h"
#include "BUZZER.h"

// Default frequency for beeps
#define DEFAULT_BEEP_FREQ   1000 // 1kHz
#define SHORT_BEEP_DURATION 100  // 100ms
#define LONG_BEEP_DURATION  500  // 500ms

/**
 * @brief Initialize buzzer on PWM4
 */
void Buzzer_Init(void)
{
    // Configure GPIO pin for PWM4 output (PA12)
    GPIOA_ModeCfg(GPIO_Pin_12, GPIO_ModeOut_PP_5mA);

    // Configure PWM clock
    PWMX_CLKCfg(4);               // cycle = 4/Fsys
    PWMX_CycleCfg(PWMX_Cycle_64); // period = 64*cycle
}

/**
 * @brief Set buzzer frequency
 *
 * @param freq Frequency in Hz (approximate)
 * @return uint16_t Actual frequency set
 */
uint16_t Buzzer_SetFrequency(uint16_t freq)
{
    uint16_t duty;

    // Calculate duty cycle for 50% duty
    duty = 32; // 50% of 64

    // Enable PWM4 output
    PWMX_ACTOUT(CH_PWM4, duty, Low_Level, ENABLE);

    return freq;
}

/**
 * @brief Turn buzzer on
 *
 * @param freq Frequency in Hz
 */
void Buzzer_On(uint16_t freq)
{
    Buzzer_SetFrequency(freq);
}

/**
 * @brief Turn buzzer off
 */
void Buzzer_Off(void)
{
    // Disable PWM4 output
    PWMX_ACTOUT(CH_PWM4, 0, Low_Level, DISABLE);
}

/**
 * @brief Generate a short beep
 *
 * @param freq Frequency in Hz (optional, use 0 for default)
 */
void Buzzer_ShortBeep(uint16_t freq)
{
    // Use default frequency if 0 is provided
    if (freq == 0) {
        freq = DEFAULT_BEEP_FREQ;
    }

    // Turn on buzzer
    Buzzer_On(freq);

    // Wait for short duration
    DelayMs(SHORT_BEEP_DURATION);

    // Turn off buzzer
    Buzzer_Off();
}

/**
 * @brief Generate a long beep
 *
 * @param freq Frequency in Hz (optional, use 0 for default)
 */
void Buzzer_LongBeep(uint16_t freq)
{
    // Use default frequency if 0 is provided
    if (freq == 0) {
        freq = DEFAULT_BEEP_FREQ;
    }

    // Turn on buzzer
    Buzzer_On(freq);

    // Wait for long duration
    DelayMs(LONG_BEEP_DURATION);

    // Turn off buzzer
    Buzzer_Off();
}