/********************************** (C) COPYRIGHT *******************************
 * File Name          : FLASH.c
 * Author             : WCH
 * Version            : V1.0
 * Date               : 2021/10/19
 * Description        :
 *******************************************************************************/

/******************************************************************************/
#include "FLASH.h"

/**************************************************************************************************
 *                                        GLOBAL VARIABLES
 **************************************************************************************************/
// Define the bonded device info structure
BONDED_DEVICE_INFO_t BondedDevInfo;
PASSCODE_INFO_t PasscodeInfo;

// Address in flash to store bonded device information
#define BONDED_DEV_ADDR       0
#define BONDED_DEV_FLASH_SIZE (8 * 1024) // 8KB for bonded device info
#define BONDED_DEV_LEN        sizeof(BONDED_DEVICE_INFO_t)

// #define PASSCODE_INFO_ADDR    (BONDED_DEV_ADDR + BONDED_DEV_FLASH_SIZE)
#define PASSCODE_INFO_ADDR 0
#define PASSCODE_INFO_LEN  256
/**************************************************************************************************
 * @fn      HAL_ChecksumCalculate
 *
 * @brief   Calculate buffer checksum
 *
 * @param   p_buf - address of buffer
 *          len - length of buffer
 *
 * @return  Checksum value
 **************************************************************************************************/
uint8_t HAL_FlashChecksumCalculate(uint8_t *p_buf, uint16_t len)
{
    uint8_t check_sum = 0;

    for (uint16_t i = 0; i < len; ++i) {
        check_sum = (uint8_t)(check_sum + p_buf[i]);
    }

    return check_sum;
}

/**************************************************************************************************
 * @fn      HAL_FlashChecksumCheck
 *
 * @brief   Check buffer checksum
 *
 * @param   p_buf - address of buffer
 *          len - length of buffer
 *
 * @return  SUCCESS or FAILURE
 **************************************************************************************************/
uint8_t HAL_FlashChecksumCheck(uint8_t *p_buf, uint16_t len)
{
    uint16_t i;
    uint8_t check_sum = 0;

    for (i = 0; i < len; ++i) {
        check_sum = (uint8_t)(check_sum + p_buf[i]);
    }

    if (check_sum == p_buf[i]) {
        return SUCCESS;
    } else {
        return FAILURE;
    }
}

/**************************************************************************************************
 * @fn      HAL_SaveBondedDevInfo
 *
 * @brief   Save bonded device information in flash
 *
 * @param   None
 *
 * @return  SUCCESS or FAILURE
 **************************************************************************************************/
uint8_t HAL_SaveBondedDevInfo(void)
{
    // Erase the flash sector if needed
    if (EEPROM_ERASE(BONDED_DEV_ADDR, BONDED_DEV_FLASH_SIZE) != SUCCESS) {
        PRINT("ERROR: Failed to erase flash for bonded device info\r\n");
        return FAILURE;
    }

    // Calculate checksum
    BondedDevInfo.CheckFlag = CHECK_VALUE;
    BondedDevInfo.Checksum  = HAL_FlashChecksumCalculate((uint8_t *)&BondedDevInfo, BONDED_DEV_LEN - 1);

    // Write to flash
    if (EEPROM_WRITE(BONDED_DEV_ADDR, &BondedDevInfo, BONDED_DEV_LEN) != SUCCESS) {
        PRINT("ERROR: Failed to write bonded device info to flash\r\n");
        return FAILURE;
    }

    PRINT("Bonded device info saved to flash. Devices: %d\r\n", BondedDevInfo.InfoNum);
    return SUCCESS;
}

/**************************************************************************************************
 * @fn      HAL_ReadBondedDevInfo
 *
 * @brief   Read bonded device information from flash
 *
 * @param   None
 *
 * @return  SUCCESS or FAILURE
 **************************************************************************************************/
uint8_t HAL_ReadBondedDevInfo(void)
{
    // Read from flash
    EEPROM_READ(BONDED_DEV_ADDR, &BondedDevInfo, BONDED_DEV_LEN);

    // Validate data
    if ((BondedDevInfo.CheckFlag != CHECK_VALUE) ||
        (HAL_FlashChecksumCheck((uint8_t *)&BondedDevInfo, BONDED_DEV_LEN - 1))) {
        // Invalid data, initialize structure
        tmos_memset((uint8_t *)&BondedDevInfo, 0, BONDED_DEV_LEN);
        BondedDevInfo.CheckFlag = CHECK_VALUE;
        BondedDevInfo.InfoNum   = 0;

        // Save initialized structure
        if (HAL_SaveBondedDevInfo() != SUCCESS) {
            PRINT("ERROR: Failed to initialize bonded device info\r\n");
            return FAILURE;
        }
    }

    PRINT("Read bonded device info from flash. Devices: %d\r\n", BondedDevInfo.InfoNum);
    return SUCCESS;
}

/**************************************************************************************************
 * @fn      HAL_AddBondedDevice
 *
 * @brief   Add a device to the bonded device list
 *
 * @param   addr - MAC address of the device to add
 *
 * @return  SUCCESS or FAILURE
 **************************************************************************************************/
uint8_t HAL_AddBondedDevice(uint8_t *addr)
{
    // Check if device is already in the list
    if (HAL_IsBondedDevice(addr)) {
        PRINT("Device already in bonded list\r\n");
        return SUCCESS;
    }

    // Check if we have space
    if (BondedDevInfo.InfoNum >= PERIPHERAL_MAX_CONNECTION) {
        PRINT("ERROR: Bonded device list full\r\n");
        return FAILURE;
    }

    // Add device to list
    tmos_memcpy(BondedDevInfo.BondedDevAddr[BondedDevInfo.InfoNum], addr, 6);
    BondedDevInfo.InfoNum++;

    // Save to flash
    return HAL_SaveBondedDevInfo();
}

/**************************************************************************************************
 * @fn      HAL_RemoveBondedDevice
 *
 * @brief   Remove a device from the bonded device list
 *
 * @param   addr - MAC address of the device to remove
 *
 * @return  SUCCESS or FAILURE
 **************************************************************************************************/
uint8_t HAL_RemoveBondedDevice(uint8_t *addr)
{
    uint8_t i, j;
    uint8_t found = FALSE;

    // Find the device in the list
    for (i = 0; i < BondedDevInfo.InfoNum; i++) {
        if (tmos_memcmp(BondedDevInfo.BondedDevAddr[i], addr, 6)) {
            found = TRUE;
            break;
        }
    }

    if (!found) {
        PRINT("Device not found in bonded list\r\n");
        return FAILURE;
    }

    // Shift remaining devices up
    for (j = i; j < BondedDevInfo.InfoNum - 1; j++) {
        tmos_memcpy(BondedDevInfo.BondedDevAddr[j], BondedDevInfo.BondedDevAddr[j + 1], 6);
    }

    // Decrement count
    BondedDevInfo.InfoNum--;

    // Save to flash
    return HAL_SaveBondedDevInfo();
}

/**************************************************************************************************
 * @fn      HAL_IsBondedDevice
 *
 * @brief   Check if a device is in the bonded device list
 *
 * @param   addr - MAC address to check
 *
 * @return  TRUE if device is bonded, FALSE otherwise
 **************************************************************************************************/
uint8_t HAL_IsBondedDevice(uint8_t *addr)
{
    uint8_t i;

    for (i = 0; i < BondedDevInfo.InfoNum; i++) {
        if (tmos_memcmp(BondedDevInfo.BondedDevAddr[i], addr, 6)) {
            return TRUE;
        }
    }

    return FALSE;
}

/**************************************************************************************************
 * @fn      HAL_ClearAllBondedDevices
 *
 * @brief   Clear all bonded device information
 *
 * @param   None
 *
 * @return  SUCCESS or FAILURE
 **************************************************************************************************/
uint8_t HAL_ClearAllBondedDevices(void)
{
    // Reset the structure
    tmos_memset((uint8_t *)&BondedDevInfo, 0, BONDED_DEV_LEN);
    BondedDevInfo.CheckFlag = CHECK_VALUE;
    BondedDevInfo.InfoNum   = 0;

    // Save to flash
    return HAL_SaveBondedDevInfo();
}

/**************************************************************************************************
 * @fn      HAL_SavePasscode
 *
 * @brief   Save passcode to flash
 *
 * @param   passcode - The passcode to save
 *
 * @return  SUCCESS or FAILURE
 **************************************************************************************************/
uint8_t HAL_SavePasscode(uint32_t passcode)
{
    // Erase the flash sector
    if (EEPROM_ERASE(PASSCODE_INFO_ADDR, 8 * 1024) != SUCCESS) {
        PRINT("ERROR: Failed to erase flash for passcode\r\n");
        return FAILURE;
    }

    // Set up the passcode structure
    PasscodeInfo.CheckFlag = CHECK_VALUE;
    PasscodeInfo.Passcode  = passcode;
    PasscodeInfo.Checksum  = HAL_FlashChecksumCalculate((uint8_t *)&PasscodeInfo, sizeof(PASSCODE_INFO_t) - 1);

    // Write to flash
    if (EEPROM_WRITE(PASSCODE_INFO_ADDR, &PasscodeInfo, sizeof(PASSCODE_INFO_t)) != SUCCESS) {
        PRINT("ERROR: Failed to write passcode to flash\r\n");
        return FAILURE;
    }

    PRINT("Passcode saved to flash: %06d\r\n", (int)passcode);
    return SUCCESS;
}

/**************************************************************************************************
 * @fn      HAL_ReadPasscode
 *
 * @brief   Read passcode from flash
 *
 * @param   None
 *
 * @return  SUCCESS or FAILURE
 **************************************************************************************************/
uint8_t HAL_ReadPasscode(void)
{
    // Read from flash
    // do {
    //     EEPROM_READ(PASSCODE_INFO_ADDR, &PasscodeInfo, 1);
    // } while (PasscodeInfo.CheckFlag != 0xFF); // Poll flash until find where the device information is stored
    EEPROM_READ(PASSCODE_INFO_ADDR, &PasscodeInfo, sizeof(PASSCODE_INFO_t));

    // Validate data
    // need validate checksum
    if (PasscodeInfo.CheckFlag != CHECK_VALUE) {
        // Invalid data, initialize structure with default passcode
        PRINT("Invalid passcode in flash, using default: %06d\r\n", (int)DEFAULT_PASSCODE);
        tmos_memset((uint8_t *)&PasscodeInfo, 0, sizeof(PASSCODE_INFO_t));
        PasscodeInfo.CheckFlag = CHECK_VALUE;
        PasscodeInfo.Passcode  = DEFAULT_PASSCODE;

        // Save initialized structure
        if (HAL_SavePasscode(DEFAULT_PASSCODE) != SUCCESS) {
            PRINT("ERROR: Failed to initialize passcode\r\n");
            return FAILURE;
        }
    }

    PRINT("Read passcode from flash: %06d\r\n", (int)PasscodeInfo.Passcode);
    return SUCCESS;
}

/**************************************************************************************************
 * @fn      HAL_GetPasscode
 *
 * @brief   Get the current passcode
 *
 * @param   None
 *
 * @return  Current passcode value
 **************************************************************************************************/
uint32_t HAL_GetPasscode(void)
{
    return PasscodeInfo.Passcode;
}

/******************************** endfile @ bond ******************************/
