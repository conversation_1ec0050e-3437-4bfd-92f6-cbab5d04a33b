/*---------------------------------------
- WeAct Studio Official Link
- taobao: weactstudio.taobao.com
- aliexpress: weactstudio.aliexpress.com
- github: github.com/WeActStudio
- gitee: gitee.com/WeAct-TC
- blog: www.weact-tc.cn
---------------------------------------*/

#include "CH59x_common.h"
#include "CONFIG.h"
#include "HAL.h"
#include "peripheral.h"
#include <FLASH.h>

static uint8_t press_count       = 0;
static uint32_t first_press_time = 0;
__attribute__((aligned(4))) uint32_t MEM_BUF[BLE_MEMHEAP_SIZE / 4];

#if (defined(BLE_MAC)) && (BLE_MAC == TRUE)
const uint8_t MacAddr[6] =
    {0x84, 0xC2, 0xE4, 0x03, 0x02, 0x02};
#endif

/*********************************************************************
 * @fn      Main_Circulation
 *
 * @brief   ��ѭ��
 *
 * @return  none
 */
__HIGH_CODE
__attribute__((noinline)) void Main_Circulation()
{
    while (1) {
        TMOS_SystemProcess();
    }
}

void key_callback(uint8_t keys)
{
    if (keys & HAL_KEY_SW_1) {
        printf("Reset bonded!\r\n");
        uint8_t bondCount;
        GAPBondMgr_GetParameter(GAPBOND_BOND_COUNT, &bondCount);
        printf("bondCount: %d\r\n", bondCount);

        uint32_t now = TMOS_GetSystemClock();
        if (press_count == 0) {
            first_press_time = now;
        }

        press_count++;

        if ((now - first_press_time) > MS1_TO_SYSTEM_TIME(3000)) {
            // Too slow, reset counter
            press_count      = 1;
            first_press_time = now;
        }

        printf("key1 pressed %d time(s)\r\n", press_count);

        if (press_count >= 5) {
            printf("Reset bonded!\r\n");
            HalLedSet(HAL_LED_ALL, HAL_LED_MODE_OFF);
            HalLedBlink(1, 2, 30, 1000);
            Peripheral_EraseAllBonds();
            press_count = 0; // Reset counter after action
        }
    }
}
/*********************************************************************
 * @fn      main
 *
 * @brief   ������
 *
 * @return  none
 */
int main()
{
#if (defined(DCDC_ENABLE)) && (DCDC_ENABLE == TRUE)
    PWR_DCDCCfg(ENABLE);
#endif
    SetSysClock(CLK_SOURCE_PLL_60MHz);
#if (defined(HAL_SLEEP)) && (HAL_SLEEP == TRUE)
    GPIOA_ModeCfg(GPIO_Pin_All, GPIO_ModeIN_PU);
    GPIOB_ModeCfg(GPIO_Pin_All, GPIO_ModeIN_PU);
#endif
#ifdef DEBUG
    GPIOA_SetBits(bTXD1);
    GPIOA_ModeCfg(bTXD1, GPIO_ModeOut_PP_5mA);
    UART1_DefInit();

#if 0 // 读写Data-Flash
    uint16_t i;
    uint8_t  s;
    uint8_t TestBuf[1024];
    PRINT("EEPROM_READ...\n");
    EEPROM_READ(0, TestBuf, 500);
    for(i = 0; i < 500; i++)
    {
        PRINT("%02x ", TestBuf[i]);
    }
    PRINT("\n");

    s = EEPROM_ERASE(0, EEPROM_BLOCK_SIZE);
    PRINT("EEPROM_ERASE=%02x\n", s);
    PRINT("EEPROM_READ...\n");
    EEPROM_READ(0, TestBuf, 500);
    for(i = 0; i < 500; i++)
    {
        PRINT("%02x ", TestBuf[i]);
    }
    PRINT("\n");

    for(i = 0; i < 500; i++)
        TestBuf[i] = 0x0 + i;
    s = EEPROM_WRITE(0, TestBuf, 500);
    PRINT("EEPROM_WRITE=%02x\n", s);
    PRINT("EEPROM_READ...\n");
    EEPROM_READ(0, TestBuf, 500);
    for(i = 0; i < 500; i++)
    {
        PRINT("%02x ", TestBuf[i]);
    }
    PRINT("\n");

#endif
#endif
    PRINT("%s\r\n", VER_LIB);
    CH59x_BLEInit();
    HAL_Init();
    Buzzer_Init();
    uint32_t passcode;
    if (HAL_ReadPasscode() == SUCCESS) {
        passcode = HAL_GetPasscode();
    } else {
        passcode = DEFAULT_PASSCODE;
    }
    GAPRole_PeripheralInit();
    // GAPRole_CentralInit();
    Peripheral_Init(passcode);
    // Central_Init();
    HalKeyConfig(key_callback);
    Main_Circulation();
}
