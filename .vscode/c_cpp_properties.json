{"configurations": [{"name": "Linux", "includePath": ["/opt/riscv/gcc12/lib/gcc/riscv-wch-elf/12.2.0/include", "/opt/riscv/gcc12/lib/gcc/riscv-wch-elf/12.2.0/include-fixed", "/opt/riscv/gcc12/riscv-wch-elf/include", "${workspaceFolder}/**", "${workspaceFolder}/StdPeriphDriver/inc", "${workspaceFolder}/ble/LIB", "${workspaceFolder}/RVMSIS", "${workspaceFolder}/ble/Profile/include", "${workspaceFolder}/ble/HAL/include", "${workspaceFolder}/ble/APP/include", "${workspaceFolder}/User"], "defines": ["DEBUG=1", "DCDC_ENABLE=1", "HAL_KEY=1", "HAL_LED=1", "CLK_OSC32K=0"], "cStandard": "c99", "cppStandard": "c++20", "intelliSenseMode": "gcc-x64", "compilerPath": "/opt/riscv/gcc12/bin/riscv-wch-elf-gcc", "compilerArgs": ["-march=rv32imac_zicsr", "-mabi=ilp32", "-mcmodel=medany"], "configurationProvider": "ms-vscode.cmake-tools"}], "version": 4}