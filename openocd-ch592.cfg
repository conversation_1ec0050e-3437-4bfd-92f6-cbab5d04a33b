# Custom OpenOCD configuration for CH592
# Alternative configuration if the default doesn't work

# WCH-Link interface
source [find interface/wch-riscv.cfg]

# Transport selection
transport select sdi

# Target configuration
set _CHIPNAME riscv
jtag newtap $_CHIPNAME cpu -irlen 5

set _TARGETNAME $_CHIPNAME.cpu
target create $_TARGETNAME riscv -chain-position $_TARGETNAME

# Configure RISC-V specific settings
riscv set_reset_timeout_sec 120
riscv set_command_timeout_sec 120

# Reset configuration
reset_config none

# Working area for flash programming
$_TARGETNAME configure -work-area-phys 0x20000000 -work-area-size 0x1000 -work-area-backup 1

# Flash configuration for CH592
flash bank $_CHIPNAME.flash wch_riscv 0x00000000 0 0 0 $_TARGETNAME

# Initialization
init_reset init

# Custom reset sequence for CH592
proc wch_reset_init {} {
    echo "Performing WCH reset sequence..."
    reset halt
    sleep 100
}
