cmake_minimum_required(VERSION 3.15)

# Project name
project(broadcaster C ASM)

# MCU Settings
set(MCU_FAMILY "CH592")
set(MCU_MODEL "CH592F")
set(CPU_FLAGS "-march=rv32imac_zicsr -mabi=ilp32 -mcmodel=medany -msmall-data-limit=8 -mno-save-restore")

# Toolchain settings
set(CMAKE_SYSTEM_NAME Generic)
set(CMAKE_SYSTEM_PROCESSOR RISC-V)
# set(TOOLCHAIN_PATH /opt/riscv/gcc12/bin)
set(TOOLCHAIN_PREFIX /opt/riscv/gcc12/bin/riscv-wch-elf-)
set(CMAKE_C_COMPILER ${TOOLCHAIN_PREFIX}gcc)
set(CMAKE_CXX_COMPILER ${TOOLCHAIN_PREFIX}g++)
set(CMAKE_ASM_COMPILER ${TOOLCHAIN_PREFIX}gcc)
set(CMAKE_OBJCOPY ${TOOLCHAIN_PREFIX}objcopy)
set(CMAKE_SIZE ${TOOLCHAIN_PREFIX}size)
set(CMAKE_LINK_DEPENDS_NO_SHARED TRUE)
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

# Compiler flags
set(COMMON_FLAGS "${CPU_FLAGS} -ffunction-sections -fdata-sections")
set(CMAKE_C_FLAGS "${COMMON_FLAGS} -std=gnu99 -Os")
set(CMAKE_ASM_FLAGS "${COMMON_FLAGS}")
set(LD_FLAGS "-Wl,--gc-sections -Wl,--print-memory-usage -nostartfiles -T${CMAKE_SOURCE_DIR}/Ld/Link.ld")
set(CMAKE_EXE_LINKER_FLAGS "${CPU_FLAGS} --specs=nano.specs --specs=nosys.specs -ffreestanding ${LD_FLAGS}" CACHE INTERNAL "")

# Project definitions
add_definitions(
    -DDEBUG=1
    -DDCDC_ENABLE=1
    -DHAL_KEY=1
    -DHAL_LED=1
    -DCLK_OSC32K=0
)

# Include directories
include_directories(
    ${CMAKE_SOURCE_DIR}/StdPeriphDriver/inc
    ${CMAKE_SOURCE_DIR}/ble/LIB
    ${CMAKE_SOURCE_DIR}/Ld
    ${CMAKE_SOURCE_DIR}/RVMSIS
    ${CMAKE_SOURCE_DIR}/ble/Profile/include
    ${CMAKE_SOURCE_DIR}/ble/HAL/include
    ${CMAKE_SOURCE_DIR}/ble/APP/include
    ${CMAKE_SOURCE_DIR}/User
)

# Source files
file(GLOB ASM_SOURCES 
    "Startup/*.S"
    "ble/LIB/*.S"
)
file(GLOB_RECURSE C_SOURCES
    "Startup/*.c"
    "RVMSIS/*.c"
    "StdPeriphDriver/CH59x_*.c"
    "User/*.c"
    "ble/APP/*.c"
    "ble/Profile/*.c"
    "ble/HAL/*.c"
)

# Exclude specific peripheral drivers
set(EXCLUDED_SOURCES
    "StdPeriphDriver/CH59x_pwm.c"
    "StdPeriphDriver/CH59x_spi0.c"
    "StdPeriphDriver/CH59x_timer0.c"
    "StdPeriphDriver/CH59x_timer1.c"
    "StdPeriphDriver/CH59x_timer2.c"
    "StdPeriphDriver/CH59x_timer3.c"
    "StdPeriphDriver/CH59x_uart2.c"
    "StdPeriphDriver/CH59x_uart3.c"
    "StdPeriphDriver/CH59x_usbdev.c"
    "StdPeriphDriver/CH59x_usbhostBase.c"
    "StdPeriphDriver/CH59x_usbhostClass.c"
)
list(REMOVE_ITEM C_SOURCES ${EXCLUDED_SOURCES})


# Create static libraries
add_library(ISP592 STATIC IMPORTED)
set_target_properties(ISP592 PROPERTIES
    IMPORTED_LOCATION "${CMAKE_SOURCE_DIR}/StdPeriphDriver/libISP592.a"
)

add_library(CH59xBLE STATIC IMPORTED)
set_target_properties(CH59xBLE PROPERTIES
    IMPORTED_LOCATION "${CMAKE_SOURCE_DIR}/ble/LIB/LIBCH59xBLE.a"
)

# Create executable
add_executable(${PROJECT_NAME}.elf)
target_sources(${PROJECT_NAME}.elf PRIVATE ${ASM_SOURCES} ${C_SOURCES})

# Link libraries
target_link_libraries(${PROJECT_NAME}.elf
    ISP592
    CH59xBLE
)

# Create hex file
add_custom_command(TARGET ${PROJECT_NAME}.elf POST_BUILD
    COMMAND ${CMAKE_OBJCOPY} -O ihex ${PROJECT_NAME}.elf ${PROJECT_NAME}.hex
    COMMAND ${CMAKE_SIZE} --format=berkeley "${PROJECT_NAME}.elf"
)

# Custom clean target
set_directory_properties(PROPERTIES ADDITIONAL_CLEAN_FILES
    "${PROJECT_NAME}.hex;${PROJECT_NAME}.map"
)

# Debug information
message(STATUS "MCU: ${MCU_MODEL}")
message(STATUS "CPU Flags: ${CPU_FLAGS}")
message(STATUS "Toolchain prefix: ${TOOLCHAIN_PREFIX}")
message(STATUS "C sources: ${C_SOURCES}")
message(STATUS "ASM sources: ${ASM_SOURCES}")
