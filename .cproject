<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<?fileVersion 4.0.0?><cproject storage_type_id="org.eclipse.cdt.core.XmlProjectDescriptionStorage">
  <storageModule moduleId="org.eclipse.cdt.core.settings">
    <cconfiguration id="ilg.gnumcueclipse.managedbuild.cross.riscv.config.elf.release.**********">
      <storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="ilg.gnumcueclipse.managedbuild.cross.riscv.config.elf.release.**********" moduleId="org.eclipse.cdt.core.settings" name="obj">
        <macros/>
        <externalSettings/>
        <extensions>
          <extension id="org.eclipse.cdt.core.ELF" point="org.eclipse.cdt.core.BinaryParser"/>
          <extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
          <extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
          <extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
          <extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
          <extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
        </extensions>
      </storageModule>
      <storageModule moduleId="cdtBuildSystem" version="4.0.0">
        <configuration artifactExtension="elf" artifactName="${ProjName}" buildArtefactType="org.eclipse.cdt.build.core.buildArtefactType.exe" buildProperties="org.eclipse.cdt.build.core.buildArtefactType=org.eclipse.cdt.build.core.buildArtefactType.exe,org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.release" cleanCommand="${cross_rm} -rf" description="" errorParsers="org.eclipse.cdt.core.GASErrorParser;org.eclipse.cdt.core.GmakeErrorParser;org.eclipse.cdt.core.GLDErrorParser;org.eclipse.cdt.core.CWDLocator;org.eclipse.cdt.core.GCCErrorParser" id="ilg.gnumcueclipse.managedbuild.cross.riscv.config.elf.release.**********" name="obj" parent="ilg.gnumcueclipse.managedbuild.cross.riscv.config.elf.release" postannouncebuildStep="" postbuildStep="" preannouncebuildStep="" prebuildStep="">
          <folderInfo id="ilg.gnumcueclipse.managedbuild.cross.riscv.config.elf.release.**********" name="/" resourcePath="">
            <toolChain id="ilg.gnumcueclipse.managedbuild.cross.riscv.toolchain.elf.release.231146001" name="RISC-V Cross GCC" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.toolchain.elf.release">
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.rvGcc.1171217701" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.rvGcc" value="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.rvGcc.8" valueType="enumerated"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.isa.base.1900297968" name="Architecture" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.isa.base" useByScannerDiscovery="false" value="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.arch.rv32i" valueType="enumerated"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.isa.multiply.1509705449" name="Multiply extension (RVM)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.isa.multiply" useByScannerDiscovery="false" value="true" valueType="boolean"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.isa.atomic.1590833110" name="Atomic extension (RVA)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.isa.atomic" useByScannerDiscovery="false" value="true" valueType="boolean"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.isa.fp.1709872289" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.isa.fp" useByScannerDiscovery="false" value="ilg.gnumcueclipse.managedbuild.cross.riscv.option.isa.fp.none" valueType="enumerated"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.isa.compressed.1038505275" name="Compressed extension (RVC)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.isa.compressed" useByScannerDiscovery="false" value="true" valueType="boolean"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.isa.xw.1505432023" name="Extra Compressed extension (RVXW)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.isa.xw" useByScannerDiscovery="false" value="false" valueType="boolean"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.isa.b.1896185078" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.isa.b" useByScannerDiscovery="false" value="false" valueType="boolean"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.isa.zmmul.724822239" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.isa.zmmul" useByScannerDiscovery="false" value="false" valueType="boolean"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.abi.integer.387605487" name="Integer ABI" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.abi.integer" useByScannerDiscovery="false" value="ilg.gnumcueclipse.managedbuild.cross.riscv.option.abi.integer.ilp32" valueType="enumerated"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.abi.fp.966196099" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.abi.fp" useByScannerDiscovery="false" value="ilg.gnumcueclipse.managedbuild.cross.riscv.option.abi.fp.none" valueType="enumerated"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.tune.394563202" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.tune" useByScannerDiscovery="false" value="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.tune.default" valueType="enumerated"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.codemodel.105093140" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.codemodel" useByScannerDiscovery="false" value="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.codemodel.any" valueType="enumerated"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.smalldatalimit.1147643442" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.smalldatalimit" useByScannerDiscovery="false" value="8" valueType="string"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.align.322546450" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.align" useByScannerDiscovery="false" value="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.align.default" valueType="enumerated"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.saverestore.367800619" name="Small prologue/epilogue (-msave-restore)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.saverestore" useByScannerDiscovery="false" value="false" valueType="boolean"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.other.1526047739" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.other" useByScannerDiscovery="true" value="" valueType="string"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.level.514997414" name="Optimization Level" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.level" useByScannerDiscovery="true" value="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.level.size" valueType="enumerated"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.messagelength.1008570639" name="Message length (-fmessage-length=0)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.messagelength" useByScannerDiscovery="true" value="true" valueType="boolean"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.signedchar.467272439" name="'char' is signed (-fsigned-char)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.signedchar" useByScannerDiscovery="true" value="true" valueType="boolean"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.functionsections.2047756949" name="Function sections (-ffunction-sections)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.functionsections" useByScannerDiscovery="true" value="true" valueType="boolean"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.datasections.207613650" name="Data sections (-fdata-sections)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.datasections" useByScannerDiscovery="true" value="true" valueType="boolean"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.nocommon.358586167" name="No common unitialized (-fno-common)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.nocommon" useByScannerDiscovery="true" value="true" valueType="boolean"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.noinlinefunctions.1298414520" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.noinlinefunctions" useByScannerDiscovery="true" value="false" valueType="boolean"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.freestanding.213924425" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.freestanding" useByScannerDiscovery="true" value="false" valueType="boolean"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.nobuiltin.2007120903" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.nobuiltin" useByScannerDiscovery="true" value="false" valueType="boolean"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.spconstant.938841347" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.spconstant" useByScannerDiscovery="true" value="false" valueType="boolean"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.PIC.234574726" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.PIC" useByScannerDiscovery="true" value="false" valueType="boolean"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.lto.1002322664" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.lto" useByScannerDiscovery="true" value="false" valueType="boolean"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.nomoveloopinvariants.1270575343" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.nomoveloopinvariants" useByScannerDiscovery="true" value="false" valueType="boolean"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.mrs.highcode.114339272" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.mrs.highcode" useByScannerDiscovery="true" value="false" valueType="boolean"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.mrs.asmsoftlib.896763512" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.mrs.asmsoftlib" useByScannerDiscovery="true" value="false" valueType="boolean"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.mrs.pipe.1835231981" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.mrs.pipe" useByScannerDiscovery="true" value="false" valueType="boolean"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.mrs.caret.2021231049" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.mrs.caret" useByScannerDiscovery="true" value="false" valueType="boolean"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.other.*********" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.other" useByScannerDiscovery="true" value="" valueType="string"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.warnings.syntaxonly.1145714735" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.warnings.syntaxonly" useByScannerDiscovery="true" value="false" valueType="boolean"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.warnings.pedantic.1546854128" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.warnings.pedantic" useByScannerDiscovery="true" value="false" valueType="boolean"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.warnings.pedanticerrors.*********" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.warnings.pedanticerrors" useByScannerDiscovery="true" value="false" valueType="boolean"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.warnings.nowarn.*********" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.warnings.nowarn" useByScannerDiscovery="true" value="false" valueType="boolean"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.warnings.unused.1961191588" name="Warn on various unused elements (-Wunused)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.warnings.unused" useByScannerDiscovery="true" value="true" valueType="boolean"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.warnings.uninitialized.929829166" name="Warn on uninitialized variables (-Wuninitialized)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.warnings.uninitialized" useByScannerDiscovery="true" value="true" valueType="boolean"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.warnings.allwarn.1168626160" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.warnings.allwarn" useByScannerDiscovery="true" value="false" valueType="boolean"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.warnings.extrawarn.291772487" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.warnings.extrawarn" useByScannerDiscovery="true" value="false" valueType="boolean"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.warnings.missingdeclaration.102344169" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.warnings.missingdeclaration" useByScannerDiscovery="true" value="false" valueType="boolean"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.warnings.conversion.550923640" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.warnings.conversion" useByScannerDiscovery="true" value="false" valueType="boolean"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.warnings.pointerarith.773148082" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.warnings.pointerarith" useByScannerDiscovery="true" value="false" valueType="boolean"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.warnings.padded.1788238782" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.warnings.padded" useByScannerDiscovery="true" value="false" valueType="boolean"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.warnings.shadow.427580978" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.warnings.shadow" useByScannerDiscovery="true" value="false" valueType="boolean"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.warnings.logicalop.1501889551" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.warnings.logicalop" useByScannerDiscovery="true" value="false" valueType="boolean"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.warnings.agreggatereturn.1785504720" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.warnings.agreggatereturn" useByScannerDiscovery="true" value="false" valueType="boolean"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.warnings.floatequal.134298453" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.warnings.floatequal" useByScannerDiscovery="true" value="false" valueType="boolean"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.warnings.toerrors.1117291056" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.warnings.toerrors" useByScannerDiscovery="true" value="false" valueType="boolean"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.warnings.other.1918769559" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.warnings.other" useByScannerDiscovery="true" value="" valueType="string"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.debugging.level.1204865254" name="Debug level" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.debugging.level" useByScannerDiscovery="true" value="ilg.gnumcueclipse.managedbuild.cross.riscv.option.debugging.level.default" valueType="enumerated"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.debugging.format.867779652" name="Debug format" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.debugging.format" useByScannerDiscovery="true" value="ilg.gnumcueclipse.managedbuild.cross.riscv.option.debugging.format.default" valueType="enumerated"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.debugging.prof.2131276390" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.debugging.prof" useByScannerDiscovery="true" value="false" valueType="boolean"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.debugging.gprof.1910159761" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.debugging.gprof" useByScannerDiscovery="true" value="false" valueType="boolean"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.debugging.other.1654431258" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.debugging.other" useByScannerDiscovery="true" value="" valueType="string"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.toolchain.name.1218760634" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.toolchain.name" useByScannerDiscovery="false" value="GNU MCU RISC-V GCC" valueType="string"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.prefix.103341323" name="Prefix" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.prefix" useByScannerDiscovery="false" value="riscv-none-embed-" valueType="string"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.c.487601824" name="C compiler" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.c" useByScannerDiscovery="false" value="gcc" valueType="string"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.cpp.1062130429" name="C++ compiler" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.cpp" useByScannerDiscovery="false" value="g++" valueType="string"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.ar.1194282993" name="Archiver" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.ar" useByScannerDiscovery="false" value="ar" valueType="string"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.objcopy.1529355265" name="Hex/Bin converter" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.objcopy" useByScannerDiscovery="false" value="objcopy" valueType="string"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.objdump.1053750745" name="Listing generator" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.objdump" useByScannerDiscovery="false" value="objdump" valueType="string"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.size.1441326233" name="Size command" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.size" useByScannerDiscovery="false" value="size" valueType="string"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.make.550105535" name="Build command" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.make" useByScannerDiscovery="false" value="make" valueType="string"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.rm.719280496" name="Remove command" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.rm" useByScannerDiscovery="false" value="rm" valueType="string"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.toolchain.id.226017994" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.toolchain.id" useByScannerDiscovery="false" value="512258282" valueType="string"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.addtools.createflash.1311852988" name="Create flash image" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.addtools.createflash" useByScannerDiscovery="false" value="true" valueType="boolean"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.addtools.createlisting.1983282875" name="Create extended listing" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.addtools.createlisting" useByScannerDiscovery="false" value="true" valueType="boolean"/>
              <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.addtools.printsize.1000761142" name="Print size" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.addtools.printsize" useByScannerDiscovery="false" value="true" valueType="boolean"/>
              <targetPlatform archList="all" binaryParser="org.eclipse.cdt.core.ELF" id="ilg.gnumcueclipse.managedbuild.cross.riscv.targetPlatform.1944008784" isAbstract="false" osList="all" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.targetPlatform"/>
              <builder buildPath="${workspace_loc:/broadcaster/obj" id="ilg.gnumcueclipse.managedbuild.cross.riscv.builder.1421508906" keepEnvironmentInBuildfile="false" managedBuildOn="true" name="Gnu Make Builder" parallelBuildOn="true" parallelizationNumber="optimal" stopOnErr="true" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.builder"/>
              <tool id="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.assembler.1244756189" name="GNU RISC-V Cross Assembler" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.assembler">
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.assembler.usepreprocessor.1692176068" name="Use preprocessor" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.assembler.usepreprocessor" useByScannerDiscovery="false" value="true" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.assembler.nostdinc.821897907" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.assembler.nostdinc" useByScannerDiscovery="true" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.assembler.preprocessonly.1205312655" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.assembler.preprocessonly" useByScannerDiscovery="false" value="false" valueType="boolean"/>
                <option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.assembler.defs.181380015" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.assembler.defs" useByScannerDiscovery="true" valueType="definedSymbols"/>
                <option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.assembler.undefs.1514976831" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.assembler.undefs" useByScannerDiscovery="true" valueType="undefDefinedSymbols"/>
                <option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.assembler.include.paths.1034038285" name="Include paths (-I)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.assembler.include.paths" useByScannerDiscovery="true" valueType="includePath"/>
                <option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.assembler.include.systempaths.496720673" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.assembler.include.systempaths" useByScannerDiscovery="true" valueType="includePath"/>
                <option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.assembler.include.files.1898455566" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.assembler.include.files" useByScannerDiscovery="true" valueType="includeFiles"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.assembler.otherwarnings.1717778600" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.assembler.otherwarnings" useByScannerDiscovery="true" value="" valueType="string"/>
                <option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.assembler.flags.1578223870" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.assembler.flags" useByScannerDiscovery="false" valueType="stringList"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.assembler.asmlisting.1937791148" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.assembler.asmlisting" useByScannerDiscovery="false" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.assembler.savetemps.119863881" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.assembler.savetemps" useByScannerDiscovery="false" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.assembler.verbose.1408057941" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.assembler.verbose" useByScannerDiscovery="false" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.assembler.other.1308239903" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.assembler.other" useByScannerDiscovery="false" value="" valueType="string"/>
                <inputType id="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.assembler.input.126366858" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.assembler.input"/>
              </tool>
              <tool id="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.c.compiler.**********" name="GNU RISC-V Cross C Compiler" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.c.compiler">
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.nostdinc.1633344562" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.nostdinc" useByScannerDiscovery="true" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.preprocessonly.208069239" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.preprocessonly" useByScannerDiscovery="false" value="false" valueType="boolean"/>
                <option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.defs.177116515" name="Defined symbols (-D)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.defs" useByScannerDiscovery="true" valueType="definedSymbols">
                  <listOptionValue builtIn="false" value="DEBUG=1"/>
                  <listOptionValue builtIn="false" value="DCDC_ENABLE=1"/>
                  <listOptionValue builtIn="false" value="HAL_KEY=1"/>
                  <listOptionValue builtIn="false" value="HAL_LED=1"/>
                  <listOptionValue builtIn="false" value="CLK_OSC32K=0"/>
                </option>
                <option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.undef.1820512625" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.undef" useByScannerDiscovery="true" valueType="undefDefinedSymbols"/>
                <option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.include.paths.1567947810" name="Include paths (-I)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.include.paths" useByScannerDiscovery="true" valueType="includePath">
                  <listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/StdPeriphDriver/inc}&quot;"/>
                  <listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/ble/LIB}&quot;"/>
                  <listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/Ld}&quot;"/>
                  <listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/RVMSIS}&quot;"/>
                  <listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/ble/Profile/include}&quot;"/>
                  <listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/ble/HAL/include}&quot;"/>
                  <listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/ble/APP/include}&quot;"/>
                </option>
                <option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.include.systempaths.2011720354" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.include.systempaths" useByScannerDiscovery="true" valueType="includePath"/>
                <option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.include.files.542153928" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.include.files" useByScannerDiscovery="true" valueType="includeFiles"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.std.2020844713" name="Language standard" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.std" useByScannerDiscovery="true" value="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.std.gnu99" valueType="enumerated"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.otheroptimizations.92321033" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.otheroptimizations" useByScannerDiscovery="true" value="" valueType="string"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.warning.missingprototypes.1180998530" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.warning.missingprototypes" useByScannerDiscovery="true" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.warning.strictprototypes.684937223" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.warning.strictprototypes" useByScannerDiscovery="true" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.warning.badfunctioncast.1090658371" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.warning.badfunctioncast" useByScannerDiscovery="true" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.otherwarnings.882361093" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.otherwarnings" useByScannerDiscovery="true" value="" valueType="string"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.asmlisting.1019398219" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.asmlisting" useByScannerDiscovery="false" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.savetemps.1858747105" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.savetemps" useByScannerDiscovery="false" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.verbose.658438318" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.verbose" useByScannerDiscovery="false" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.other.1132663916" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.other" useByScannerDiscovery="true" value="" valueType="string"/>
                <inputType id="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.c.compiler.input.**********" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.c.compiler.input"/>
              </tool>
              <tool id="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.cpp.compiler.1610882921" name="GNU RISC-V Cross C++ Compiler" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.cpp.compiler">
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.nostdinc.1306818359" name="Do not search system directories (-nostdinc)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.nostdinc" useByScannerDiscovery="true" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.nostdincpp.411115799" name="Do not search system C++ directories (-nostdinc++)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.nostdincpp" useByScannerDiscovery="true" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.preprocessonly.704235280" name="Preprocess only (-E)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.preprocessonly" useByScannerDiscovery="false" value="false" valueType="boolean"/>
                <option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.defs.256688978" name="Defined symbols (-D)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.defs" useByScannerDiscovery="true" valueType="definedSymbols"/>
                <option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.undef.1769659537" name="Undefined symbols (-U)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.undef" useByScannerDiscovery="true" valueType="undefDefinedSymbols"/>
                <option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.include.paths.1641430352" name="Include paths (-I)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.include.paths" useByScannerDiscovery="true" valueType="includePath"/>
                <option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.include.systempaths.1217742259" name="Include system paths (-isystem)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.include.systempaths" useByScannerDiscovery="true" valueType="includePath"/>
                <option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.include.files.1922780842" name="Include files (-include)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.include.files" useByScannerDiscovery="true" valueType="includeFiles"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.std.426208505" name="Language standard" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.std" useByScannerDiscovery="true" value="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.std.gnucpp11" valueType="enumerated"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.abiversion.1671052931" name="ABI version" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.abiversion" useByScannerDiscovery="true" value="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.abiversion.0" valueType="enumerated"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.noexceptions.1891612477" name="Do not use exceptions (-fno-exceptions)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.noexceptions" useByScannerDiscovery="true" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.nortti.76308566" name="Do not use RTTI (-fno-rtti)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.nortti" useByScannerDiscovery="true" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.nousecxaatexit.390229569" name="Do not use _cxa_atexit() (-fno-use-cxa-atexit)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.nousecxaatexit" useByScannerDiscovery="true" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.nothreadsafestatics.1143999424" name="Do not use thread-safe statics (-fno-threadsafe-statics)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.nothreadsafestatics" useByScannerDiscovery="true" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.otheroptimizations.1589858535" name="Other optimization flags" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.otheroptimizations" useByScannerDiscovery="true" value="" valueType="string"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.warnabi.1304561076" name="Warn on ABI violations (-Wabi)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.warnabi" useByScannerDiscovery="true" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.warning.ctordtorprivacy.534922494" name="Warn on class privacy (-Wctor-dtor-privacy)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.warning.ctordtorprivacy" useByScannerDiscovery="true" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.warning.noexcept.1102421587" name="Warn on no-except expressions (-Wnoexcept)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.warning.noexcept" useByScannerDiscovery="true" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.warning.nonvirtualdtor.249412631" name="Warn on virtual destructors (-Wnon-virtual-dtor)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.warning.nonvirtualdtor" useByScannerDiscovery="true" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.warning.strictnullsentinel.1022346732" name="Warn on uncast NULL (-Wstrict-null-sentinel)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.warning.strictnullsentinel" useByScannerDiscovery="true" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.warning.signpromo.1510049602" name="Warn on sign promotion (-Wsign-promo)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.warning.signpromo" useByScannerDiscovery="true" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.warneffc.1507924344" name="Warn about Effective C++ violations (-Weffc++)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.warneffc" useByScannerDiscovery="true" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.otherwarnings.521572828" name="Other warning flags" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.otherwarnings" useByScannerDiscovery="true" value="" valueType="string"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.asmlisting.842958016" name="Generate assembler listing (-Wa,-adhlns=&quot;$@.lst&quot;)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.asmlisting" useByScannerDiscovery="false" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.savetemps.715871177" name="Save temporary files (--save-temps Use with caution!)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.savetemps" useByScannerDiscovery="false" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.verbose.974774820" name="Verbose (-v)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.verbose" useByScannerDiscovery="false" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.other.1293392631" name="Other compiler flags" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.other" useByScannerDiscovery="true" value="" valueType="string"/>
                <inputType id="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.cpp.compiler.input.1079228323" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.cpp.compiler.input"/>
              </tool>
              <tool id="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.c.linker.1620074387" name="GNU RISC-V Cross C Linker" outputPrefix="" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.c.linker">
                <option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.scriptfile.1390103472" name="Script files (-T)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.scriptfile" useByScannerDiscovery="false" valueType="stringList">
                  <listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/Ld/Link.ld}&quot;"/>
                </option>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.nostart.913830613" name="Do not use standard start files (-nostartfiles)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.nostart" useByScannerDiscovery="false" value="true" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.nodeflibs.1285997013" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.nodeflibs" useByScannerDiscovery="false" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.nostdlibs.179047434" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.nostdlibs" useByScannerDiscovery="false" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.gcsections.194760422" name="Remove unused sections (-Xlinker --gc-sections)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.gcsections" useByScannerDiscovery="false" value="true" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.printgcsections.270824644" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.printgcsections" useByScannerDiscovery="false" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.strip.1802601885" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.strip" useByScannerDiscovery="false" value="false" valueType="boolean"/>
                <option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.libs.813115939" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.libs" useByScannerDiscovery="false" valueType="libs">
                  <listOptionValue builtIn="false" value="ISP592"/>
                  <listOptionValue builtIn="false" value="CH59xBLE"/>
                </option>
                <option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.paths.2057340378" name="Library search path (-L)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.paths" useByScannerDiscovery="false" valueType="libPaths">
                  <listOptionValue builtIn="false" value="../"/>
                  <listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/StdPeriphDriver}&quot;"/>
                  <listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/ble/LIB}&quot;"/>
                </option>
                <option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.flags.1125808200" name="Linker flags (-Xlinker [option])" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.flags" useByScannerDiscovery="false" valueType="stringList">
                  <listOptionValue builtIn="false" value="--print-memory-usage"/>
                </option>
                <option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.otherobjs.16994550" name="Other objects" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.otherobjs" useByScannerDiscovery="false" valueType="userObjs"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.mapfilename.789195953" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.mapfilename" useByScannerDiscovery="false" value="&quot;${BuildArtifactFileBaseName}.map&quot;" valueType="string"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.picolibc.62047318" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.picolibc" useByScannerDiscovery="false" value="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.picolibc.disabled" valueType="enumerated"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.cref.824432654" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.cref" useByScannerDiscovery="false" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.printmap.751686263" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.printmap" useByScannerDiscovery="false" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.usenewlibnano.239404511" name="Use newlib-nano (--specs=nano.specs)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.usenewlibnano" useByScannerDiscovery="false" value="true" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.usenewlibnosys.351964161" name="Do not use syscalls (--specs=nosys.specs)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.usenewlibnosys" useByScannerDiscovery="false" value="true" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.useprintffloat.695795083" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.useprintffloat" useByScannerDiscovery="false" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.usescanffloat.1839373535" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.usescanffloat" useByScannerDiscovery="false" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.verbose.1444336626" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.verbose" useByScannerDiscovery="false" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.printfloat.2044235126" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.printfloat" useByScannerDiscovery="false" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.printf.888161142" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.printf" useByScannerDiscovery="false" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.iqmath.1390292521" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.iqmath" useByScannerDiscovery="false" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.other.1683775650" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.other" useByScannerDiscovery="false" value="" valueType="string"/>
                <inputType id="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.c.linker.input.1859223768" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.c.linker.input">
                  <additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
                  <additionalInput kind="additionalinput" paths="$(LIBS)"/>
                </inputType>
              </tool>
              <tool id="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.cpp.linker.1947503520" name="GNU RISC-V Cross C++ Linker" outputPrefix="" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.cpp.linker">
                <option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.scriptfile.1751226764" name="Script files (-T)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.scriptfile" useByScannerDiscovery="false" valueType="stringList">
                  <listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/Ld/Link.ld}&quot;"/>
                </option>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.nostart.642896175" name="Do not use standard start files (-nostartfiles)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.nostart" useByScannerDiscovery="false" value="true" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.nodeflibs.282300763" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.nodeflibs" useByScannerDiscovery="false" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.nostdlibs.924960428" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.nostdlibs" useByScannerDiscovery="false" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.gcsections.1689063433" name="Remove unused sections (-Xlinker --gc-sections)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.gcsections" useByScannerDiscovery="false" value="true" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.printgcsections.621524254" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.printgcsections" useByScannerDiscovery="false" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.strip.679063538" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.strip" useByScannerDiscovery="false" value="false" valueType="boolean"/>
                <option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.libs.579700779" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.libs" useByScannerDiscovery="false" valueType="libs">
                  <listOptionValue builtIn="false" value="ISP592"/>
                  <listOptionValue builtIn="false" value="CH59xBLE"/>
                </option>
                <option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.paths.1029177148" name="Library search path (-L)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.paths" useByScannerDiscovery="false" valueType="libPaths">
                  <listOptionValue builtIn="false" value="../"/>
                  <listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/StdPeriphDriver}&quot;"/>
                  <listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/ble/LIB}&quot;"/>
                </option>
                <option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.flags.1251620602" name="Linker flags (-Xlinker [option])" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.flags" useByScannerDiscovery="false" valueType="stringList">
                  <listOptionValue builtIn="false" value="--print-memory-usage"/>
                </option>
                <option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.otherobjs.1493906625" name="Other objects" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.otherobjs" useByScannerDiscovery="false" valueType="userObjs"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.mapfilename.1354773182" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.mapfilename" useByScannerDiscovery="false" value="&quot;${BuildArtifactFileBaseName}.map&quot;" valueType="string"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.picolibc.4345436542" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.picolibc" useByScannerDiscovery="false" value="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.picolibc.disabled" valueType="enumerated"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.cref.1007621036" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.cref" useByScannerDiscovery="false" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.printmap.2073713641" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.printmap" useByScannerDiscovery="false" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.usenewlibnano.1540675679" name="Use newlib-nano (--specs=nano.specs)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.usenewlibnano" useByScannerDiscovery="false" value="true" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.usenewlibnosys.561457319" name="Do not use syscalls (--specs=nosys.specs)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.usenewlibnosys" useByScannerDiscovery="false" value="true" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.useprintffloat.1497004994" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.useprintffloat" useByScannerDiscovery="false" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.usescanffloat.881728961" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.usescanffloat" useByScannerDiscovery="false" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.verbose.922041698" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.verbose" useByScannerDiscovery="false" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.printfloat.476377985" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.printfloat" useByScannerDiscovery="false" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.printf.626387227" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.printf" useByScannerDiscovery="false" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.iqmath.1441123220" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.iqmath" useByScannerDiscovery="false" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.other.1748689212" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.other" useByScannerDiscovery="false" value="" valueType="string"/>
              </tool>
              <tool id="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.archiver.1292785366" name="GNU RISC-V Cross Archiver" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.archiver"/>
              <tool id="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.createflash.1801165667" name="GNU RISC-V Cross Create Flash Image" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.createflash">
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createflash.textsection.1097396305" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createflash.textsection" useByScannerDiscovery="false" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createflash.datasection.2034511797" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createflash.datasection" useByScannerDiscovery="false" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createflash.choice.1726268709" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createflash.choice" useByScannerDiscovery="false" value="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createflash.choice.ihex" valueType="enumerated"/>
                <option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createflash.othersection.1890795928" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createflash.othersection" useByScannerDiscovery="false" valueType="stringList"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createflash.other.788974495" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createflash.other" useByScannerDiscovery="false" value="" valueType="string"/>
              </tool>
              <tool id="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.createlisting.1356766765" name="GNU RISC-V Cross Create Listing" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.createlisting">
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createlisting.source.2052761852" name="Display source (--source|-S)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createlisting.source" useByScannerDiscovery="false" value="true" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createlisting.allheaders.439659821" name="Display all headers (--all-headers|-x)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createlisting.allheaders" useByScannerDiscovery="false" value="true" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createlisting.demangle.67111865" name="Demangle names (--demangle|-C)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createlisting.demangle" useByScannerDiscovery="false" value="true" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createlisting.debugging.1623481730" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createlisting.debugging" useByScannerDiscovery="false" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createlisting.disassemble.1859590835" name="Disassemble (--disassemble|-d)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createlisting.disassemble" useByScannerDiscovery="false" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createlisting.fileheaders.160868348" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createlisting.fileheaders" useByScannerDiscovery="false" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createlisting.linenumbers.1549373929" name="Display line numbers (--line-numbers|-l)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createlisting.linenumbers" useByScannerDiscovery="false" value="true" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createlisting.reloc.1008747895" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createlisting.reloc" useByScannerDiscovery="false" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createlisting.symbols.577922241" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createlisting.symbols" useByScannerDiscovery="false" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createlisting.wide.1298918921" name="Wide lines (--wide|-w)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createlisting.wide" useByScannerDiscovery="false" value="true" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createlisting.other.1560864108" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createlisting.other" useByScannerDiscovery="false" value="" valueType="string"/>
              </tool>
              <tool id="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.printsize.712424314" name="GNU RISC-V Cross Print Size" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.printsize">
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.printsize.format.1404031980" name="Size format" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.printsize.format" useByScannerDiscovery="false" value="ilg.gnumcueclipse.managedbuild.cross.riscv.option.printsize.format.berkeley" valueType="enumerated"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.printsize.hex.176087647" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.printsize.hex" useByScannerDiscovery="false" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.printsize.totals.380903440" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.printsize.totals" useByScannerDiscovery="false" value="false" valueType="boolean"/>
                <option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.printsize.other.1271150877" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.printsize.other" useByScannerDiscovery="false" value="" valueType="string"/>
              </tool>
            </toolChain>
          </folderInfo>
          <sourceEntries>
            <entry excluding="StdPeriphDriver/CH59x_usbhostClass.c|StdPeriphDriver/CH59x_usbhostBase.c|StdPeriphDriver/CH59x_usbdev.c|StdPeriphDriver/CH59x_uart3.c|StdPeriphDriver/CH59x_uart2.c|StdPeriphDriver/CH59x_timer3.c|StdPeriphDriver/CH59x_timer2.c|StdPeriphDriver/CH59x_timer1.c|StdPeriphDriver/CH59x_timer0.c|StdPeriphDriver/CH59x_spi0.c|StdPeriphDriver/CH59x_pwm.c" flags="VALUE_WORKSPACE_PATH|RESOLVED" kind="sourcePath" name=""/>
          </sourceEntries>
        </configuration>
      </storageModule>
      <storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
      <storageModule moduleId="ilg.gnumcueclipse.managedbuild.packs"/>
    </cconfiguration>
  </storageModule>
  <storageModule moduleId="cdtBuildSystem" version="4.0.0">
    <project id="999.ilg.gnumcueclipse.managedbuild.cross.riscv.target.elf.275846018" name="Executable file" projectType="ilg.gnumcueclipse.managedbuild.cross.riscv.target.elf"/>
  </storageModule>
  <storageModule moduleId="scannerConfiguration">
    <autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
    <scannerConfigBuildInfo instanceId="ilg.gnumcueclipse.managedbuild.cross.riscv.config.elf.debug.767917625;ilg.gnumcueclipse.managedbuild.cross.riscv.config.elf.debug.767917625.;ilg.gnumcueclipse.managedbuild.cross.riscv.tool.c.compiler.1375371130;ilg.gnumcueclipse.managedbuild.cross.riscv.tool.c.compiler.input.1473381709">
      <autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
    </scannerConfigBuildInfo>
    <scannerConfigBuildInfo instanceId="ilg.gnumcueclipse.managedbuild.cross.riscv.config.elf.release.**********;ilg.gnumcueclipse.managedbuild.cross.riscv.config.elf.release.**********.;ilg.gnumcueclipse.managedbuild.cross.riscv.tool.c.compiler.**********;ilg.gnumcueclipse.managedbuild.cross.riscv.tool.c.compiler.input.**********">
      <autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
    </scannerConfigBuildInfo>
  </storageModule>
  <storageModule moduleId="org.eclipse.cdt.core.LanguageSettingsProviders"/>
  <storageModule moduleId="org.eclipse.cdt.make.core.buildtargets"/>
</cproject>