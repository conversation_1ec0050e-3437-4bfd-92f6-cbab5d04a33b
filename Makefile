# Makefile for CH592 BLE Broadcaster Project
# Based on the CMakeLists.txt configuration

# Project settings
PROJECT_NAME = broadcaster
MCU_FAMILY = CH592
MCU_MODEL = CH592F

# Toolchain settings
TOOLCHAIN_PREFIX = /opt/riscv/gcc12/bin/riscv-wch-elf-
CC = $(TOOLCHAIN_PREFIX)gcc
CXX = $(TOOLCHAIN_PREFIX)g++
AS = $(TOOLCHAIN_PREFIX)gcc
OBJCOPY = $(TOOLCHAIN_PREFIX)objcopy
SIZE = $(TOOLCHAIN_PREFIX)size
OBJDUMP = $(TOOLCHAIN_PREFIX)objdump

# CPU and architecture flags
CPU_FLAGS = -march=rv32imac_zicsr -mabi=ilp32 -mcmodel=medany -msmall-data-limit=8 -mno-save-restore

# Compiler flags
COMMON_FLAGS = $(CPU_FLAGS) -ffunction-sections -fdata-sections
CFLAGS = $(COMMON_FLAGS) -std=gnu99 -Os -g
ASFLAGS = $(COMMON_FLAGS)

# Linker flags
LDFLAGS = $(CPU_FLAGS) --specs=nano.specs --specs=nosys.specs -ffreestanding \
          -Wl,--gc-sections -Wl,--print-memory-usage -nostartfiles \
          -T$(PWD)/Ld/Link.ld

# Project definitions
DEFINES = -DDEBUG=1 -DDCDC_ENABLE=1 -DHAL_KEY=1 -DHAL_LED=1 -DCLK_OSC32K=0

# Include directories
INCLUDES = -I$(PWD)/StdPeriphDriver/inc \
           -I$(PWD)/ble/LIB \
           -I$(PWD)/Ld \
           -I$(PWD)/RVMSIS \
           -I$(PWD)/ble/Profile/include \
           -I$(PWD)/ble/HAL/include \
           -I$(PWD)/ble/APP/include \
           -I$(PWD)/User

# Source directories
STARTUP_DIR = Startup
RVMSIS_DIR = RVMSIS
STDPERIPH_DIR = StdPeriphDriver
USER_DIR = User
BLE_APP_DIR = ble/APP
BLE_PROFILE_DIR = ble/Profile
BLE_HAL_DIR = ble/HAL
BLE_LIB_DIR = ble/LIB

# Assembly sources
ASM_SOURCES = $(wildcard $(STARTUP_DIR)/*.S) \
              $(wildcard $(BLE_LIB_DIR)/*.S)

# C sources
C_SOURCES = $(wildcard $(STARTUP_DIR)/*.c) \
            $(wildcard $(RVMSIS_DIR)/*.c) \
            $(wildcard $(USER_DIR)/*.c) \
            $(wildcard $(BLE_APP_DIR)/*.c) \
            $(wildcard $(BLE_PROFILE_DIR)/*.c) \
            $(wildcard $(BLE_HAL_DIR)/*.c)

# Standard peripheral driver sources (excluding some unused ones)
STDPERIPH_SOURCES = $(STDPERIPH_DIR)/CH59x_adc.c \
                    $(STDPERIPH_DIR)/CH59x_clk.c \
                    $(STDPERIPH_DIR)/CH59x_flash.c \
                    $(STDPERIPH_DIR)/CH59x_gpio.c \
                    $(STDPERIPH_DIR)/CH59x_i2c.c \
                    $(STDPERIPH_DIR)/CH59x_lcd.c \
                    $(STDPERIPH_DIR)/CH59x_pwm.c \
                    $(STDPERIPH_DIR)/CH59x_pwr.c \
                    $(STDPERIPH_DIR)/CH59x_sys.c \
                    $(STDPERIPH_DIR)/CH59x_uart0.c \
                    $(STDPERIPH_DIR)/CH59x_uart1.c

# Add standard peripheral sources to C sources
C_SOURCES += $(STDPERIPH_SOURCES)

# Static libraries
LIBS = -L$(PWD)/StdPeriphDriver -lISP592 \
       -L$(PWD)/ble/LIB -lCH59xBLE

# Object files
ASM_OBJECTS = $(ASM_SOURCES:.S=.o)
C_OBJECTS = $(C_SOURCES:.c=.o)
OBJECTS = $(ASM_OBJECTS) $(C_OBJECTS)

# Build directory
BUILD_DIR = build
BUILD_OBJECTS = $(addprefix $(BUILD_DIR)/, $(OBJECTS))

# Output files
ELF_FILE = $(BUILD_DIR)/$(PROJECT_NAME).elf
HEX_FILE = $(BUILD_DIR)/$(PROJECT_NAME).hex
MAP_FILE = $(BUILD_DIR)/$(PROJECT_NAME).map
LST_FILE = $(BUILD_DIR)/$(PROJECT_NAME).lst

# Default target
all: $(HEX_FILE) size

# Create build directory
$(BUILD_DIR):
	@mkdir -p $(BUILD_DIR)
	@mkdir -p $(BUILD_DIR)/$(STARTUP_DIR)
	@mkdir -p $(BUILD_DIR)/$(RVMSIS_DIR)
	@mkdir -p $(BUILD_DIR)/$(STDPERIPH_DIR)
	@mkdir -p $(BUILD_DIR)/$(USER_DIR)
	@mkdir -p $(BUILD_DIR)/$(BLE_APP_DIR)
	@mkdir -p $(BUILD_DIR)/$(BLE_PROFILE_DIR)
	@mkdir -p $(BUILD_DIR)/$(BLE_HAL_DIR)
	@mkdir -p $(BUILD_DIR)/$(BLE_LIB_DIR)

# Compile C sources
$(BUILD_DIR)/%.o: %.c | $(BUILD_DIR)
	@echo "CC $<"
	@$(CC) $(CFLAGS) $(DEFINES) $(INCLUDES) -c $< -o $@

# Compile assembly sources
$(BUILD_DIR)/%.o: %.S | $(BUILD_DIR)
	@echo "AS $<"
	@$(AS) $(ASFLAGS) $(DEFINES) $(INCLUDES) -c $< -o $@

# Link ELF file
$(ELF_FILE): $(BUILD_OBJECTS)
	@echo "LD $@"
	@$(CC) $(LDFLAGS) -Wl,-Map=$(MAP_FILE) $(BUILD_OBJECTS) $(LIBS) -o $@

# Create HEX file
$(HEX_FILE): $(ELF_FILE)
	@echo "OBJCOPY $@"
	@$(OBJCOPY) -O ihex $< $@

# Create listing file
$(LST_FILE): $(ELF_FILE)
	@echo "OBJDUMP $@"
	@$(OBJDUMP) -h -S $< > $@

# Show size information
size: $(ELF_FILE)
	@echo "Size information:"
	@$(SIZE) --format=berkeley $<

# Flash the firmware (you may need to adjust this for your programmer)
flash: $(HEX_FILE)
	@echo "Flashing $(HEX_FILE)..."
	@echo "Please configure your flash tool command here"
	# Example: openocd -f interface/your-programmer.cfg -f target/ch592.cfg -c "program $(HEX_FILE) verify reset exit"

# Clean build files
clean:
	@echo "Cleaning build files..."
	@rm -rf $(BUILD_DIR)

# Clean all generated files
distclean: clean
	@rm -f *.hex *.elf *.map *.lst

# Debug target - compile with debug symbols
debug: CFLAGS += -g3 -O0
debug: $(HEX_FILE)

# Release target - optimize for size
release: CFLAGS += -Os -DNDEBUG
release: $(HEX_FILE)

# Show help
help:
	@echo "Available targets:"
	@echo "  all      - Build the project (default)"
	@echo "  clean    - Remove build files"
	@echo "  distclean- Remove all generated files"
	@echo "  debug    - Build with debug symbols"
	@echo "  release  - Build optimized release version"
	@echo "  flash    - Flash the firmware to target"
	@echo "  size     - Show size information"
	@echo "  help     - Show this help message"

# Phony targets
.PHONY: all clean distclean debug release flash size help

# Print variables for debugging
print-%:
	@echo $* = $($*)
